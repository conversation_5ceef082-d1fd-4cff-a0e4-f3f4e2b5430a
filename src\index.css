@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;1,300;1,400;1,500;1,600&family=Playfair+Display:ital,wght@0,400;0,500;0,600;1,400;1,500;1,600&family=Montserrat:wght@300;400;500;600;700&family=Bubblegum+Sans&family=Open+Sans:wght@300;400;500;600;700&display=swap');
@import 'leaflet/dist/leaflet.css';
@import './styles/auth-luxury.css';
@import './styles/last-section-luxury.css';
@import './styles/cookie-consent-luxury.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Luxury Color Palette - Light Theme (WCAG AA Compliant) */
    --background: 45 15% 95%; /* Luxury cream background #F2EEE6 */
    --foreground: 25 25% 12%; /* Deep charcoal text for luxury contrast */
    --card: 45 20% 92%; /* Luxury light card background */
    --card-foreground: 25 25% 12%; /* Deep charcoal card text */
    --popover: 45 20% 92%; /* Luxury light popover */
    --popover-foreground: 25 25% 12%; /* Deep charcoal popover text */
    --primary: 35 35% 65%; /* Luxury gold primary #D4C2A4 */
    --primary-foreground: 25 25% 12%; /* Deep charcoal on primary */
    --secondary: 25 25% 15%; /* Deep luxury charcoal secondary */
    --secondary-foreground: 45 15% 95%; /* Luxury cream on secondary */
    --muted: 45 10% 88%; /* Luxury muted background */
    --muted-foreground: 25 20% 35%; /* Luxury muted text */
    --accent: 35 35% 65%; /* Luxury gold accent #D4C2A4 */
    --accent-foreground: 25 25% 12%; /* Deep charcoal on accent */
    --destructive: 0 65% 45%; /* Luxury deep red for destructive */
    --destructive-foreground: 45 15% 95%; /* Luxury cream on destructive */
    --border: 35 20% 80%; /* Luxury subtle borders */
    --input: 45 15% 90%; /* Luxury input backgrounds */
    --ring: 35 35% 65%; /* Luxury gold focus ring */
    --radius: 0.5rem;
    --sidebar-background: 45 15% 95%; /* Luxury cream sidebar */
    --sidebar-foreground: 25 25% 12%; /* Deep charcoal sidebar text */
    --sidebar-primary: 35 35% 65%; /* Luxury gold sidebar primary */
    --sidebar-primary-foreground: 25 25% 12%; /* Deep charcoal sidebar primary text */
    --sidebar-accent: 45 10% 88%; /* Luxury muted sidebar accent */
    --sidebar-accent-foreground: 25 25% 12%; /* Deep charcoal sidebar accent text */
    --sidebar-border: 35 20% 80%; /* Luxury subtle sidebar border */
    --sidebar-ring: 35 35% 65%; /* Luxury gold sidebar ring */

    /* Luxury Color Variables */
    --deep-rust: 35 35% 65%; /* #D4C2A4 - Luxury gold primary */
    --warm-orange: 35 40% 60%; /* Luxury warm gold variant */
    --golden-orange: 35 35% 65%; /* #D4C2A4 - Luxury gold accent */
    --warm-cream: 45 15% 95%; /* #F2EEE6 - Luxury cream background */
    --light-warm-gray: 45 20% 92%; /* Luxury light card backgrounds */
    --deep-brown: 25 25% 15%; /* #16191D - Luxury deep charcoal */
    --dark-brown: 25 25% 12%; /* Luxury primary text */
    --medium-brown: 25 20% 35%; /* Luxury secondary text */
    --bright-orange: 35 45% 70%; /* Luxury bright gold highlights */
    --golden-yellow: 35 40% 75%; /* Luxury golden yellow accents */
    --warm-red: 0 65% 45%; /* Luxury deep red emphasis */
  }

  .dark {
    /* Luxury Color Palette - Dark Theme */
    --background: 25 25% 8%; /* Luxury deep charcoal background #16191D */
    --foreground: 45 15% 95%; /* Luxury cream text #F2EEE6 */
    --card: 25 25% 12%; /* Luxury dark charcoal cards */
    --card-foreground: 45 15% 95%; /* Luxury cream card text */
    --popover: 25 25% 12%; /* Luxury dark charcoal popover */
    --popover-foreground: 45 15% 95%; /* Luxury cream popover text */
    --primary: 35 35% 65%; /* Luxury gold primary #D4C2A4 */
    --primary-foreground: 25 25% 12%; /* Deep charcoal on primary */
    --secondary: 35 35% 65%; /* Luxury gold secondary */
    --secondary-foreground: 25 25% 8%; /* Deep charcoal on secondary */
    --muted: 25 20% 18%; /* Luxury dark muted */
    --muted-foreground: 45 10% 75%; /* Luxury light muted text */
    --accent: 35 35% 65%; /* Luxury gold accent #D4C2A4 */
    --accent-foreground: 25 25% 8%; /* Deep charcoal on accent */
    --destructive: 0 65% 55%; /* Luxury red for dark theme */
    --destructive-foreground: 45 15% 95%; /* Luxury cream on destructive */
    --border: 25 20% 25%; /* Luxury dark borders */
    --input: 25 20% 15%; /* Luxury dark input backgrounds */
    --ring: 35 35% 65%; /* Luxury gold focus ring */
    --sidebar-background: 25 25% 10%; /* Luxury deep charcoal sidebar */
    --sidebar-foreground: 45 15% 95%; /* Luxury cream sidebar text */
    --sidebar-primary: 35 35% 65%; /* Luxury gold sidebar primary */
    --sidebar-primary-foreground: 25 25% 12%; /* Deep charcoal sidebar primary text */
    --sidebar-accent: 25 20% 18%; /* Luxury dark sidebar accent */
    --sidebar-accent-foreground: 45 15% 95%; /* Luxury cream sidebar accent text */
    --sidebar-border: 25 20% 25%; /* Luxury dark sidebar border */
    --sidebar-ring: 35 35% 65%; /* Luxury gold sidebar ring */

    /* Luxury Color Variables - Dark Theme */
    --deep-rust: 35 35% 65%; /* Luxury gold for dark theme #D4C2A4 */
    --warm-orange: 35 40% 70%; /* Luxury warm gold variant for dark theme */
    --golden-orange: 35 35% 65%; /* Luxury gold accent #D4C2A4 */
    --warm-cream: 45 15% 95%; /* Luxury cream #F2EEE6 */
    --light-warm-gray: 25 20% 15%; /* Luxury dark gray for cards */
    --deep-brown: 25 25% 8%; /* Luxury deep charcoal #16191D */
    --dark-brown: 25 25% 8%; /* Luxury deep charcoal */
    --medium-brown: 25 20% 25%; /* Luxury medium dark charcoal */
    --bright-orange: 35 45% 75%; /* Luxury bright gold for dark theme */
    --golden-yellow: 35 40% 80%; /* Luxury golden yellow for dark theme */
    --warm-red: 0 65% 55%; /* Luxury red for dark theme */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }

  /* Ensure full height on mobile devices */
  html, body {
    height: 100%;
    min-height: 100vh;
    /* Fix iOS Safari viewport height issues */
    min-height: -webkit-fill-available;
  }
}

/* Mobile-first responsive utilities */
@layer utilities {
  /* Touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly spacing */
  .mobile-spacing {
    @apply px-4 sm:px-6 md:px-8;
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl;
  }

  /* Mobile navigation fixes */
  .mobile-nav-safe {
    /* Account for mobile browser UI */
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent zoom on input focus (iOS Safari) */
  .no-zoom {
    font-size: 16px;
  }

  /* Mobile-friendly button spacing */
  .mobile-button {
    @apply px-4 py-3 text-base;
  }

  @screen sm {
    .mobile-button {
      @apply px-6 py-2 text-sm;
    }
  }

  /* Responsive grid utilities */
  .grid-responsive-1 {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-responsive-2 {
    @apply grid-cols-1 md:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  /* Mobile-first card spacing */
  .card-mobile {
    @apply p-4 sm:p-6;
  }

  /* Website-Inspired Theme Utilities */
  .website-gradient-header {
    background: linear-gradient(135deg,
      rgba(234, 88, 12, 0.95) 0%,
      rgba(220, 38, 38, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(234, 88, 12, 0.2);
  }

  .website-gradient-footer {
    background: linear-gradient(45deg,
      rgba(220, 38, 38, 0.95) 0%,
      rgba(234, 88, 12, 0.95) 50%,
      rgba(220, 38, 38, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -8px 32px rgba(234, 88, 12, 0.15);
  }

  .website-gradient-primary {
    background: linear-gradient(135deg, #B85C38 0%, #A0522D 50%, #8B4513 100%);
  }

  .website-gradient-warm {
    background: linear-gradient(135deg, #E67E22 0%, #D84315 35%, #B85C38 70%, #A0522D 100%);
  }

  .website-gradient-golden {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 50%, #D84315 100%);
  }

  .website-gradient-button {
    background: linear-gradient(135deg, #B85C38 0%, #A0522D 100%);
    transition: all 0.3s ease;
  }

  .website-gradient-button:hover {
    background: linear-gradient(135deg, #D84315 0%, #B85C38 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(184, 92, 56, 0.3);
  }

  .website-text-gradient {
    background: linear-gradient(135deg, #B85C38 0%, #8B4513 50%, #6B4423 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .website-shadow {
    box-shadow: 0 4px 20px rgba(184, 92, 56, 0.15);
  }

  .website-shadow-hover:hover {
    box-shadow: 0 8px 30px rgba(184, 92, 56, 0.25);
  }

  /* Card gradient effects */
  .website-gradient-card {
    background: linear-gradient(135deg, rgba(253, 246, 227, 0.95) 0%, rgba(248, 245, 240, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(184, 92, 56, 0.1);
  }

  .website-gradient-card-hover:hover {
    background: linear-gradient(135deg, rgba(253, 246, 227, 1) 0%, rgba(248, 245, 240, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(184, 92, 56, 0.15);
  }

  /* Enhanced glassmorphism effects */
  .website-glassmorphism-light {
    background: rgba(253, 246, 227, 0.8);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .website-glassmorphism-dark {
    background: rgba(107, 68, 35, 0.8);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Section background gradients */
  .website-gradient-section-light {
    background: linear-gradient(135deg,
      rgba(253, 246, 227, 0.9) 0%,
      rgba(248, 245, 240, 0.9) 50%,
      rgba(253, 246, 227, 0.9) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .website-gradient-section-warm {
    background: linear-gradient(135deg,
      rgba(184, 92, 56, 0.1) 0%,
      rgba(160, 82, 45, 0.1) 50%,
      rgba(139, 69, 19, 0.1) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Ultra Glass Morphism Effects */
  .ultra-glass-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(40px) saturate(180%);
    -webkit-backdrop-filter: blur(40px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-text-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 16px;
  }

  .glass-button {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Legacy safari classes for backward compatibility */
  .safari-gradient-sunset {
    @apply website-gradient-primary;
  }

  .safari-gradient-earth {
    @apply website-gradient-warm;
  }

  .safari-gradient-golden {
    @apply website-gradient-golden;
  }

  .safari-text-gradient {
    @apply website-text-gradient;
  }

  .safari-shadow {
    @apply website-shadow;
  }

  .safari-shadow-hover:hover {
    @apply website-shadow-hover;
  }
}

/* Animation Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

/* Staggered Animation Delays */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Parallax Background */
.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Mobile parallax fix and performance optimizations */
@media (max-width: 768px) {
  .parallax-bg {
    background-attachment: scroll;
  }

  /* Reduce animation complexity on mobile */
  .card-3d-hover,
  .card-3d-tilt {
    transition: transform 0.3s ease;
  }

  .card-3d-hover:hover,
  .card-3d-tilt:hover {
    transform: scale(1.02) translateY(-2px);
  }

  /* Simplify complex animations on mobile */
  .animate-marquee {
    animation-duration: 60s; /* Slower for better performance */
  }

  /* Reduce parallax effects on mobile */
  .luxury-particle {
    animation: none; /* Disable particle animations on mobile */
  }

  /* Reduce blur effects on mobile for better performance */
  .glass-button,
  .luxury-glass-container {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Optimize GSAP animations for mobile */
  .gsap-mobile-optimized {
    will-change: transform;
    transform: translate3d(0, 0, 0);
  }
}

/* Seamless Transitions */
.page-transition {
  transition: all 0.3s ease-in-out;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Mobile hover states - disable on touch devices */
@media (hover: none) and (pointer: coarse) {
  .hover-scale:hover {
    transform: none;
  }
}

/* Enhanced Focus States for Better Accessibility */
*:focus-visible {
  outline: 3px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: 4px;
}

/* High contrast focus for better visibility */
@media (prefers-contrast: high) {
  *:focus-visible {
    outline: 4px solid #000;
    outline-offset: 2px;
    background-color: #ffff00;
    color: #000;
  }
}

/* Focus styles for interactive elements */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 3px solid hsl(var(--ring));
  outline-offset: 2px;
  box-shadow: 0 0 0 1px hsl(var(--background)), 0 0 0 4px hsl(var(--ring));
}

/* Skip link focus styles */
.skip-link:focus {
  outline: 3px solid hsl(var(--ring));
  outline-offset: 2px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(0);
  z-index: 9999;
}

/* Focus within containers */
.focus-within:focus-within {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Keyboard navigation indicators */
.keyboard-nav-active *:focus {
  outline: 3px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
.mouse-nav-active *:focus {
  outline: none;
}

/* Focus trap styles */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Roving tabindex styles */
[role="tablist"] [role="tab"]:focus,
[role="menu"] [role="menuitem"]:focus,
[role="listbox"] [role="option"]:focus {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  outline: 2px solid hsl(var(--ring));
  outline-offset: -2px;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  line-height: 1.2;
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  /* Prevent text selection on touch */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Better touch feedback */
  button, a {
    -webkit-tap-highlight-color: rgba(212, 194, 164, 0.2);
    min-height: 44px;
  }

  /* Improve scrolling on iOS */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Performance optimizations for mobile scrolling */
  body {
    /* Prevent scroll bounce on iOS */
    overscroll-behavior: none;
    /* Optimize scrolling performance */
    scroll-behavior: smooth;
  }

  /* GPU acceleration for animated elements */
  .animate-float,
  .animate-pulse-slow,
  .animate-fade-in,
  .animate-slide-in-up,
  .animate-scale-in,
  .animate-float-gentle,
  .animate-shimmer,
  .animate-particle-float,
  .animate-float-up-down,
  .animate-float-rotate {
    will-change: transform;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
  }

  /* Destinations page mobile optimizations */
  .intro__content {
    padding-top: env(safe-area-inset-top, 20px);
    padding-bottom: env(safe-area-inset-bottom, 20px);
  }

  .slide {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }

  .col__content {
    padding-bottom: env(safe-area-inset-bottom, 20px);
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }

  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
  }
}

/* Extra small mobile devices */
@media (max-width: 475px) {
  .intro__title {
    font-size: clamp(2.5rem, 8vw, 4rem) !important;
  }

  .col__content-title {
    font-size: clamp(1.875rem, 7vw, 3rem) !important;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders */
  .border {
    border-width: 0.5px;
  }
}

/* Loading states for better UX */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

.loading-overlay {
  @apply fixed inset-0 bg-safari-deep-brown bg-opacity-50 flex items-center justify-center z-50;
}

/* Value Propositions Background */
.value-propositions-bg {
  /* First try with a simple color to test if the class is being applied */
  background-color: #f1eee9;
  /* Then add the image overlay */
  background-image: url('https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/NEW%20SHIT/Generated%20image%203%20(1).png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  min-height: 600px;
  width: 100%;
  position: relative;
  
}

/* Fallback background color in case image doesn't load */
.value-propositions-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(241, 238, 233, 0.95);
  z-index: -1;
}

/* 3D Card Effects */
.perspective-1000 {
  perspective: 1000px;
}

.perspective-2000 {
  perspective: 2000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.transform-gpu {
  transform: translateZ(0);
  will-change: transform;
}

/* Enhanced Glass Morphism */
.glass-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.glass-card:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(255, 255, 255, 0.25) 50%,
    rgba(255, 255, 255, 0.15) 100%);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Radial Gradient Utility */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Enhanced 3D Hover Effects */
.card-3d-hover {
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}

.card-3d-hover:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(-5deg) translateZ(20px) scale(1.02);
}

/* Floating Animation for Icons */
@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-6px) rotate(2deg);
  }
}

.animate-float-gentle {
  animation: float-gentle 3s ease-in-out infinite;
}

/* Shimmer Effect */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Particle Animation */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
  33% {
    transform: translateY(-8px) translateX(4px) scale(1.1);
    opacity: 1;
  }
  66% {
    transform: translateY(-4px) translateX(-2px) scale(0.9);
    opacity: 0.8;
  }
}

.animate-particle-float {
  animation: particle-float 4s ease-in-out infinite;
}

/* Enhanced Glow Effects */
.glow-soft {
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.glow-soft:hover {
  filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
}

/* 3D Text Effects */
.text-3d {
  text-shadow:
    0 1px 0 rgba(255, 255, 255, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
}

.text-3d:hover {
  text-shadow:
    0 1px 0 rgba(255, 255, 255, 0.9),
    0 3px 6px rgba(0, 0, 0, 0.15),
    0 6px 12px rgba(0, 0, 0, 0.1);
}

/* Advanced 3D Card Transforms */
.card-3d-tilt {
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  transform-origin: center center;
}

.card-3d-tilt:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(-10deg) translateZ(30px);
}

/* Holographic Effect */
.holographic {
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.3) 75%,
    rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 200%;
  animation: holographic-shift 3s ease-in-out infinite;
}

@keyframes holographic-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Depth Shadow */
.depth-shadow {
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24),
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 8px 16px rgba(0, 0, 0, 0.08);
}

.depth-shadow:hover {
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 2px 4px rgba(0, 0, 0, 0.24),
    0 8px 16px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.08),
    0 32px 64px rgba(0, 0, 0, 0.04);
}

/* Magnetic Hover Effect */
.magnetic-hover {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic-hover:hover {
  transform: scale(1.05) translateY(-5px);
  filter: brightness(1.1) saturate(1.2);
}

/* Crystal Glass Effect */
.crystal-glass {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.2) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    rgba(255, 255, 255, 0.4) 100%);
  backdrop-filter: blur(25px) saturate(200%);
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-top: 2px solid rgba(255, 255, 255, 0.6);
  border-left: 2px solid rgba(255, 255, 255, 0.6);
}

/* Neon Glow */
.neon-glow {
  box-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
}

/* Floating Animation Variants */
@keyframes float-up-down {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-rotate {
  0%, 100% {
    transform: rotate(0deg) translateY(0px);
  }
  25% {
    transform: rotate(1deg) translateY(-3px);
  }
  50% {
    transform: rotate(0deg) translateY(-6px);
  }
  75% {
    transform: rotate(-1deg) translateY(-3px);
  }
}

.animate-float-up-down {
  animation: float-up-down 4s ease-in-out infinite;
}

.animate-float-rotate {
  animation: float-rotate 6s ease-in-out infinite;
}

/* Leaflet Map Fixes */
/* Ensure Leaflet map components stay below the header (z-50) */
.leaflet-container {
  z-index: 1 !important;
  height: 100% !important;
  width: 100% !important;
}

.leaflet-control-container {
  z-index: 10 !important;
}

.leaflet-popup-pane {
  z-index: 20 !important;
}

.leaflet-tooltip-pane {
  z-index: 25 !important;
}

.leaflet-marker-pane {
  z-index: 15 !important;
}

.leaflet-shadow-pane {
  z-index: 5 !important;
}

/* Ensure all Leaflet overlays stay below header */
.leaflet-overlay-pane {
  z-index: 8 !important;
}

/* Fix for Leaflet marker icons */
.leaflet-default-icon-path {
  background-image: url('https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png');
}

/* Ensure map tiles load properly */
.leaflet-tile-pane {
  z-index: 2 !important;
}

.leaflet-tile {
  pointer-events: none;
}

/* Custom div icon fixes */
.custom-div-icon {
  background: transparent !important;
  border: none !important;
}

/* Chatbot Mobile Optimizations */
@media (max-width: 768px) {
  /* Better touch targets for mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Prevent zoom on input focus for iOS */
  .no-zoom {
    font-size: 16px !important;
  }

  /* Improve scrolling performance on mobile */
  .chatbot-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    scroll-behavior: smooth;
  }
}

  /* Better spacing for mobile message bubbles */
  .chatbot-message {
    border-radius: 12px;
  }

  /* Optimize button spacing for touch */
  .chatbot-button {
    padding: 8px 12px;
    margin: 2px;
    border-radius: 8px;
    min-height: 36px;
  }


/* Extra small screens (phones in portrait) */
@media (max-width: 480px) {
  .chatbot-text {
    font-size: 12px;
    line-height: 1.4;
  }

  .chatbot-message {
    padding: 6px 8px;
    margin-bottom: 4px;
  }

  .touch-target {
    min-height: 40px;
    min-width: 40px;
  }
}

/* Landscape orientation on small screens */
@media (max-width: 768px) and (orientation: landscape) {
  .chatbot-container {
    max-height: calc(100vh - 80px);
  }
}

/* Accessible focus indicators */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-deep-rust focus:ring-offset-2;
}

/* Mobile-safe sticky positioning */
.sticky-mobile {
  position: -webkit-sticky;
  position: sticky;
}

/* Chatbot specific responsive utilities */
.chatbot-mobile-center {
  left: 50%;
  transform: translateX(-50%);
}

@media (min-width: 768px) {
  .chatbot-mobile-center {
    left: auto;
    transform: none;
  }
}

/* Ensure chatbot has proper z-index hierarchy */
.chatbot-widget {
  z-index: 9998;
}

.chatbot-container {
  z-index: 9999;
}

/* Chatbot content containment */
.chatbot-content-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chatbot-messages-area {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.chatbot-scroll-area {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Ensure messages stay within bounds */
.chatbot-message-container {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

/* Prevent content shift on mobile */
.chatbot-fixed-width {
  width: calc(100vw - 0.5rem);
  max-width: 22rem;
}

@media (min-width: 640px) {
  .chatbot-fixed-width {
    width: calc(100vw - 2rem);
    max-width: 24rem;
  }
}

@media (min-width: 768px) {
  .chatbot-fixed-width {
    width: 24rem;
    max-width: none;
  }
}

/* Smooth animations for mobile */
@media (prefers-reduced-motion: no-preference) {
  .chatbot-animate {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chatbot-message {
    border: 1px solid currentColor;
  }
}

/* Marquee animation improvements */
.animate-marquee {
  animation-duration: var(--duration, 40s);
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  will-change: transform;
}

/* Ensure smooth marquee performance */
@media (prefers-reduced-motion: reduce) {
  .animate-marquee {
    animation-play-state: paused;
  }
}
@keyframes slideOutLeft {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(-100%); opacity: 0; }
}
.slide-out-left {
  animation: slideOutLeft 0.5s ease-out forwards;
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}
.slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

@keyframes slideOutRight {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(100%); opacity: 0; }
}
.slide-out-right {
  animation: slideOutRight 0.5s ease-out forwards;
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}
.slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

/* Safari Showcase Section Styles */
.safari-showcase {
  font-family: 'Cormorant Garamond', serif;
}

.safari-showcase .intro__title {
  font-size: 25vw;
  line-height: 0.8;
  letter-spacing: -0.05em;
}

.safari-showcase .col__content-title {
  font-size: 18vw;
  line-height: 0.9;
  letter-spacing: -0.02em;
}

@media (min-width: 768px) {
  .safari-showcase .col__content-title {
    font-size: 11vw;
  }
}

/* Hero Section Styles */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  overflow: hidden;
  /* CSS containment for better performance */
  contain: layout style paint;
  /* Create a new stacking context */
  isolation: isolate;
}

.hero-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  transform: translate3d(-50%, -50%, 0);
  z-index: 0;
  object-fit: cover;
  /* GPU acceleration and performance optimizations */
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.hero-overlay {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5));
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  /* GPU acceleration */
  transform: translate3d(0, 0, 0);
  will-change: auto;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  /* CSS containment for better performance */
  contain: layout style;
  /* GPU acceleration */
  transform: translate3d(0, 0, 0);
}

.hero-logo {
  width: 100px;
  height: auto;
  margin-top: 70px;
}

.hero-company-name {
  font-family: 'Open Sans', ;
  font-size: 0.5rem;
  font-weight: 400;
  letter-spacing: 0.3em;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

.hero-headline {
  font-family: 'Cormorant Garamond';
  font-weight: 400;
  font-style: normal;
  font-size: 65px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  color: white;
  margin-bottom: 0.1rem;
  width: 562px;
  height: 221px;
  margin-left: auto;
  margin-right: auto;
}

.hero-headline em {
  font-style: italic;
}

.hero-cta-button {
  font-family: 'Montserrat', sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  background: transparent;
  border: 1px solid white;
  color: white;
  padding: 1rem 2.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  margin-top: 1.5rem;
}

.hero-cta-button:hover {
  background: white;
  color: black;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-headline {
    font-size: 48px;
    width: 90%;
    height: auto;
    margin-bottom: 2rem;
  }

  .hero-company-name {
    font-size: 0.75rem;
    letter-spacing: 0.2em;
    margin-bottom: 2rem;
  }

  .hero-cta-button {
    font-size: 0.75rem;
    padding: 0.875rem 2rem;
  }

  .hero-logo {
    width: 100px;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-headline {
    font-size: 36px;
    width: 95%;
    height: auto;
    margin-bottom: 1.5rem;
  }

  .hero-company-name {
    font-size: 0.625rem;
    margin-bottom: 1.5rem;
  }

  .hero-cta-button {
    font-size: 0.625rem;
    padding: 0.75rem 1.5rem;
    letter-spacing: 0.15em;
  }

  .hero-logo {
    width: 80px;
    margin-bottom: 1rem;
  }

  .hero-section {
    padding: 1rem;
  }
}

/* Custom Scrollbar Styles for Search Overlay */
.search-overlay-scroll::-webkit-scrollbar {
  width: 8px;
}

.search-overlay-scroll::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 4px;
}

.search-overlay-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.search-overlay-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Firefox scrollbar */
.search-overlay-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(55, 65, 81, 0.3);
}

/* Luxury Tours Page Custom Scrollbar */
.luxury-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.luxury-scrollbar::-webkit-scrollbar-track {
  background: rgba(22, 25, 29, 0.5);
  border-radius: 3px;
}

.luxury-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(212, 194, 164, 0.4);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.luxury-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 194, 164, 0.7);
}

/* Firefox scrollbar for luxury theme */
.luxury-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(212, 194, 164, 0.4) rgba(22, 25, 29, 0.5);
}

/* Tour Builder Destinations Scrollbar */
.destinations-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.destinations-scrollbar::-webkit-scrollbar-track {
  background: rgba(229, 231, 235, 0.5);
  border-radius: 3px;
}

.destinations-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.destinations-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8);
}

/* Firefox scrollbar for destinations */
.destinations-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.6) rgba(229, 231, 235, 0.5);
}